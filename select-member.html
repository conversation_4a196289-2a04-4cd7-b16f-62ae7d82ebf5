<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选择成员关系</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: white;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px 10px;
            font-size: 17px;
            font-weight: 600;
        }

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 20px 20px;
        }

        .back-arrow {
            font-size: 18px;
            color: #999;
            cursor: pointer;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .content {
            padding: 0 20px;
            height: calc(100% - 240px);
            overflow-y: auto;
        }

        .member-category {
            margin-bottom: 40px;
        }

        .category-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
        }

        .tag-container {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }

        .member-tag {
            padding: 8px 20px;
            background: #f0f2ff;
            color: #667eea;
            border: 1px solid #e0e6ff;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: none;
        }

        .member-tag:hover {
            background: #667eea;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .member-tag.selected {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .bottom-actions {
            position: absolute;
            bottom: 40px;
            left: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .action-btn {
            width: 100%;
            height: 50px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .wechat-btn {
            background: #07c160;
            color: white;
        }

        .wechat-btn:hover {
            background: #06ad56;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(7, 193, 96, 0.4);
        }

        .manual-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .manual-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .wechat-icon::before {
            content: '💬';
            font-size: 16px;
        }

        .manual-icon::before {
            content: '👤';
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="status-bar">
            <span class="time">9:41</span>
            <span class="signal">📶 📶 📶 🔋</span>
        </div>

        <div class="header">
            <span class="back-arrow" onclick="goBack()">←</span>
            <span class="page-title">选择成员关系</span>
        </div>

        <div class="content">
            <div class="member-category">
                <div class="category-title">成人</div>
                <div class="tag-container">
                    <div class="member-tag" data-relation="妈妈">妈妈</div>
                    <div class="member-tag" data-relation="爸爸">爸爸</div>
                    <div class="member-tag" data-relation="爷爷">爷爷</div>
                    <div class="member-tag" data-relation="奶奶">奶奶</div>
                    <div class="member-tag" data-relation="外公">外公</div>
                    <div class="member-tag" data-relation="外婆">外婆</div>
                </div>
            </div>

            <div class="member-category">
                <div class="category-title">学生</div>
                <div class="tag-container">
                    <div class="member-tag" data-relation="宝贝">宝贝</div>
                    <div class="member-tag" data-relation="哥哥">哥哥</div>
                    <div class="member-tag" data-relation="姐姐">姐姐</div>
                    <div class="member-tag" data-relation="弟弟">弟弟</div>
                    <div class="member-tag" data-relation="妹妹">妹妹</div>
                </div>
            </div>
        </div>

        <div class="bottom-actions">
            <button class="action-btn wechat-btn" onclick="inviteByWechat()">
                <span class="btn-icon wechat-icon"></span>
                微信邀请
            </button>
            <button class="action-btn manual-btn" onclick="manualAdd()">
                <span class="btn-icon manual-icon"></span>
                手动添加
            </button>
        </div>
    </div>

    <script>
        let selectedRelation = '';

        // 返回上一页
        function goBack() {
            window.history.back();
        }

        // 微信邀请
        function inviteByWechat() {
            if (!selectedRelation) {
                alert('请先选择成员关系');
                return;
            }
            
            alert(`通过微信邀请${selectedRelation}加入家庭`);
            // 这里可以添加微信邀请的逻辑
        }

        // 手动添加
        function manualAdd() {
            if (!selectedRelation) {
                alert('请先选择成员关系');
                return;
            }
            
            // 跳转到手动添加成员页面，传递选择的关系
            window.location.href = `add-member.html?relation=${selectedRelation}`;
        }

        // 标签选择处理
        function setupTagSelection() {
            const tags = document.querySelectorAll('.member-tag');
            
            tags.forEach(tag => {
                tag.addEventListener('click', function() {
                    // 移除其他标签的选中状态
                    tags.forEach(t => t.classList.remove('selected'));
                    
                    // 添加当前标签的选中状态
                    this.classList.add('selected');
                    
                    // 记录选择的关系
                    selectedRelation = this.getAttribute('data-relation');
                    
                    console.log('选择的关系:', selectedRelation);
                });
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupTagSelection();
        });
    </script>
</body>
</html>
