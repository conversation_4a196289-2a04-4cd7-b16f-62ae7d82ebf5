
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Awriter登录流程</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .phone-container {
            width: 320px;
            height: 640px;
            background: white;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px 10px;
            font-size: 14px;
            font-weight: 600;
        }

        .time { color: #000; }
        .signal { color: #000; }

        .content {
            padding: 0 30px;
            height: calc(100% - 60px);
            display: flex;
            flex-direction: column;
        }

        .step {
            display: none;
            flex: 1;
        }

        .step.active {
            display: flex;
            flex-direction: column;
        }

        /* 返回按钮 */
        .back-btn {
            position: absolute;
            left: 20px;
            top: 70px;
            font-size: 18px;
            color: #333;
            cursor: pointer;
            z-index: 10;
        }

        /* 登录页样式 */
        .login-main {
            text-align: center;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .logo {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .logo .a { color: #ff6b6b; }
        .logo .w { color: #4ecdc4; }
        .logo .r { color: #45b7d1; }
        .logo .i { color: #96ceb4; }
        .logo .t { color: #feca57; }
        .logo .e { color: #ff9ff3; }
        .logo .r2 { color: #54a0ff; }

        .subtitle {
            color: #999;
            font-size: 14px;
            margin-bottom: 80px;
        }

        .btn {
            width: 100%;
            height: 50px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .btn-tertiary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        /* 验证码登录页样式 */
        .hello-title {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin: 60px 0 10px;
        }

        .hello-subtitle {
            color: #999;
            font-size: 14px;
            margin-bottom: 40px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            color: #333;
            font-size: 14px;
            margin-bottom: 8px;
            display: block;
        }

        .form-input {
            width: 100%;
            height: 50px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 0 15px;
            font-size: 16px;
            background: #f8f8f8;
        }

        .password-group {
            position: relative;
        }

        .forgot-link {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #667eea;
            font-size: 14px;
            text-decoration: none;
            cursor: pointer;
        }

        .login-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-top: 40px;
        }

        /* 身份选择页样式 */
        .identity-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .identity-title {
            color: #999;
            font-size: 16px;
            margin-bottom: 60px;
        }

        .identity-btn {
            width: 200px;
            height: 50px;
            border: 2px solid #667eea;
            border-radius: 25px;
            background: white;
            color: #667eea;
            font-size: 16px;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .identity-btn.selected {
            background: #667eea;
            color: white;
        }

        .confirm-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-top: 40px;
        }

        /* 成功页样式 */
        .success-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
        }

        .checkmark {
            color: white;
            font-size: 40px;
            font-weight: bold;
        }

        .success-text {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 60px;
        }

        .complete-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .home-indicator {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 134px;
            height: 5px;
            background: #000;
            border-radius: 3px;
        }

        /* 动画效果 */
        .step {
            transition: opacity 0.3s ease-in-out;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="status-bar">
            <span class="time">9:41</span>
            <span class="signal">📶 📶 📶 🔋</span>
        </div>

        <!-- 返回按钮 -->
        <div class="back-btn" id="backBtn" style="display: none;">←</div>

        <!-- 步骤1: 登录页 -->
        <div class="step active" id="step1">
            <div class="content">
                <div class="login-main">
                    <div class="logo">
                        <span class="a">A</span><span class="w">w</span><span class="r">r</span><span class="i">i</span><span class="t">t</span><span class="e">e</span><span class="r2">r</span>
                    </div>
                    <div class="subtitle">智能写作助手</div>
                    <button class="btn btn-tertiary" onclick="goToStep(2)">📱 授权登录</button>
                    <button class="btn btn-secondary" onclick="goToStep(2)">📱 手机验证码</button>
                    <button class="btn btn-primary" onclick="goToStep(2)">📱 手机密码</button>
                </div>
            </div>
        </div>

        <!-- 步骤2: 手机验证码登录 -->
        <div class="step" id="step2">
            <div class="content">
                <div class="hello-title">Hello!</div>
                <div class="hello-subtitle">请使用手机验证码登录</div>
                
                <div class="form-group">
                    <label class="form-label">手机号码</label>
                    <input type="tel" class="form-input" value="13860888888">
                </div>
                
                <div class="form-group">
                    <label class="form-label">密码</label>
                    <div class="password-group">
                        <input type="password" class="form-input" placeholder="请输入验证码" id="codeInput">
                        <a href="#" class="forgot-link" onclick="sendCode()">发送验证码</a>
                    </div>
                </div>
                
                <button class="btn login-btn" onclick="goToStep(3)">登录</button>
            </div>
        </div>

        <!-- 步骤3: 多身份登录 -->
        <div class="step" id="step3">
            <div class="content">
                <div class="identity-content">
                    <div class="identity-title">请选择身份</div>
                    <button class="identity-btn selected" onclick="selectIdentity(this)">👨‍🏫 教师</button>
                    <button class="identity-btn" onclick="selectIdentity(this)">👨‍🎓 学生</button>
                    <button class="btn confirm-btn" onclick="goToStep(4)">确定</button>
                </div>
            </div>
        </div>

        <!-- 步骤4: 操作成功 -->
        <div class="step" id="step4">
            <div class="content">
                <div class="success-content">
                    <div class="success-icon">
                        <span class="checkmark">✓</span>
                    </div>
                    <div class="success-text">登录成功</div>
                    <button class="btn complete-btn" onclick="goToHomepage()">完成</button>
                </div>
            </div>
        </div>

        <div class="home-indicator"></div>
    </div>

    <script>
        let currentStep = 1;

        function goToStep(step) {
            // 隐藏当前步骤
            document.getElementById(`step${currentStep}`).classList.remove('active');
            
            // 显示目标步骤
            document.getElementById(`step${step}`).classList.add('active');
            
            // 更新当前步骤
            currentStep = step;
            
            // 控制返回按钮显示
            const backBtn = document.getElementById('backBtn');
            if (step === 1) {
                backBtn.style.display = 'none';
            } else {
                backBtn.style.display = 'block';
            }
        }

        function goToHomepage() {
            // 跳转到首页
            window.location.href = 'homepage.html';
        }

        function goBack() {
            if (currentStep > 1) {
                goToStep(currentStep - 1);
            }
        }

        function selectIdentity(btn) {
            // 移除所有选中状态
            document.querySelectorAll('.identity-btn').forEach(b => b.classList.remove('selected'));
            // 添加选中状态
            btn.classList.add('selected');
        }

        function sendCode() {
            alert('验证码已发送！');
        }

        // 返回按钮事件
        document.getElementById('backBtn').addEventListener('click', goBack);

        // 键盘事件
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && currentStep > 1) {
                goBack();
            }
        });
    </script>
</body>
</html>
