<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作文详情</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: white;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px 10px;
            font-size: 17px;
            font-weight: 600;
        }

        .time { color: #000; }
        .signal { color: #000; }

        /* 顶部导航栏 */
        .header {
            display: flex;
            align-items: center;
            padding: 10px 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .back-btn {
            font-size: 18px;
            color: #333;
            cursor: pointer;
            margin-right: 15px;
        }

        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .content {
            padding: 20px;
            height: calc(100% - 140px);
            overflow-y: auto;
        }

        /* 标签区域 */
        .tags-section {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 15px;
        }

        .tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        /* 评分区域 */
        .score-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .essay-title {
            font-size: 16px;
            color: #333;
        }

        .total-score {
            font-size: 24px;
            font-weight: bold;
            color: #ff6b6b;
        }

        .score-unit {
            font-size: 14px;
            color: #999;
        }

        /* 评价项目 */
        .evaluation-section {
            margin-bottom: 25px;
        }

        .evaluation-item {
            padding: 12px 0;
            border-bottom: 1px solid #f5f5f5;
        }

        .evaluation-item:last-child {
            border-bottom: none;
        }

        .evaluation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }

        .evaluation-label {
            font-size: 14px;
            color: #333;
        }

        .evaluation-text {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }

        .stars {
            display: flex;
            gap: 2px;
        }

        .star {
            color: #ffd700;
            font-size: 16px;
        }

        .star.empty {
            color: #ddd;
        }

        /* 总体评价 */
        .overall-review {
            margin-bottom: 30px;
        }

        .review-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .review-content {
            font-size: 14px;
            line-height: 1.6;
            color: #666;
        }

        /* 底部操作区域 */
        .bottom-actions {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #f0f0f0;
            padding: 15px 20px 30px;
        }

        .not-interested-btn {
            background: #f5f5f5;
            color: #666;
            border: none;
            border-radius: 20px;
            padding: 10px 20px;
            font-size: 14px;
            cursor: pointer;
            margin-bottom: 10px;
        }

        .stats {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #999;
        }

        /* 导航标签栏 */
        .nav-tabs {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            margin-bottom: 20px;
        }

        .nav-tab {
            flex: 1;
            text-align: center;
            padding: 15px 0;
            font-size: 16px;
            color: #666;
            cursor: pointer;
            position: relative;
            border-bottom: 2px solid transparent;
        }

        .nav-tab.active {
            color: #1976d2;
            border-bottom-color: #1976d2;
        }

        /* 标签内容区域 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="status-bar">
            <span class="time">9:41</span>
            <span class="signal">📶 📶 📶 🔋</span>
        </div>

        <div class="header">
            <div class="back-btn" onclick="goBack()">←</div>
            <div class="header-title">详情</div>
        </div>

        <div class="content">
            <!-- 导航标签栏 -->
            <div class="nav-tabs">
                <div class="nav-tab active" onclick="switchTab('essay-review')">作文点评</div>
                <div class="nav-tab" onclick="switchTab('essay-report')">作文报告</div>
                <div class="nav-tab" onclick="switchTab('model-essay')">范色作文</div>
                <div class="nav-tab" onclick="switchTab('essay-requirements')">作文要求</div>
            </div>

            <!-- 作文点评内容 -->
            <div id="essay-review-content" class="tab-content">
                <!-- 评分区域 -->
                <div class="score-section">
                    <div class="essay-title">第一单元 单元作文 全命题</div>
                    <div>
                        <span class="total-score">28</span>
                        <span class="score-unit">分</span>
                    </div>
                </div>

                <!-- 副标题 -->
                <div style="font-size: 16px; color: #333; margin-bottom: 20px;">我的植物朋友</div>

                <!-- 点评标题 -->
                <div style="font-size: 16px; font-weight: 600; color: #333; margin-bottom: 15px;">点评</div>

            <!-- 评价项目 -->
            <div class="evaluation-section">
                <div class="evaluation-item">
                    <div class="evaluation-header">
                        <div class="evaluation-label">立意新颖</div>
                        <div class="stars">
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star empty">★</span>
                        </div>
                    </div>
                    <div class="evaluation-text">文章以我的植物朋友为主题，立意清新自然，内容贴近生活，体现了对大自然的热爱和对植物的细致观察。</div>
                </div>

                <div class="evaluation-item">
                    <div class="evaluation-header">
                        <div class="evaluation-label">内容</div>
                        <div class="stars">
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star empty">★</span>
                        </div>
                    </div>
                    <div class="evaluation-text">内容具体详实，描述了植物的外形、习性、作用等多个方面，展现了作者对植物朋友的深入了解和真挚情感。</div>
                </div>

                <div class="evaluation-item">
                    <div class="evaluation-header">
                        <div class="evaluation-label">结构</div>
                        <div class="stars">
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star empty">★</span>
                        </div>
                    </div>
                    <div class="evaluation-text">文章结构较为清晰，但层次感还可以进一步加强，建议在段落安排上更加有序。</div>
                </div>

                <div class="evaluation-item">
                    <div class="evaluation-header">
                        <div class="evaluation-label">语言</div>
                        <div class="stars">
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star">★</span>
                            <span class="star empty">★</span>
                        </div>
                    </div>
                    <div class="evaluation-text">语言朴实</div>
                </div>
            </div>

                <!-- 总体评价 -->
                <div class="overall-review">
                    <div class="review-title">总体评价</div>
                    <div class="review-content">
                        本文以植物朋友为主题进行写作，立意清新，内容丰富，文章结构较为清晰，语言朴实自然。作者通过细致的观察和真挚的情感表达，成功地展现了对植物朋友的喜爱之情。文章描述生动具体，能够抓住植物的特点进行描写，体现了作者良好的观察能力。建议在今后的写作中注意段落层次的安排，使文章结构更加清晰明了。同时可以进一步丰富语言表达，运用更多的修辞手法，让文章更加生动有趣。总的来说，这是一篇较好的习作，展现了作者对自然的热爱和细致的观察力，值得肯定。希望作者继续努力，在写作的道路上不断进步。
                    </div>
                </div>
            </div>

            <!-- 作文报告内容 -->
            <div id="essay-report-content" class="tab-content">
                <div style="padding: 40px 20px; text-align: center; color: #666;">
                    <h3>作文报告</h3>
                    <p>详细的作文分析报告将在这里显示</p>
                </div>
            </div>

            <!-- 范色作文内容 -->
            <div id="model-essay-content" class="tab-content">
                <div style="padding: 40px 20px; text-align: center; color: #666;">
                    <h3>范色作文</h3>
                    <p>优秀范文示例将在这里显示</p>
                </div>
            </div>

            <!-- 作文要求内容 -->
            <div id="essay-requirements-content" class="tab-content">
                <div style="padding: 40px 20px; text-align: center; color: #666;">
                    <h3>作文要求</h3>
                    <p>作文写作要求和评分标准将在这里显示</p>
                </div>
            </div>
        </div>

        <!-- 底部操作区域 -->
        <div class="bottom-actions">
            <button class="not-interested-btn">不感兴趣</button>
            <div class="stats">
                <span>字数统计：672字</span>
                <span>阅读：100字</span>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }

        // 标签切换功能
        function switchTab(tabName) {
            // 移除所有标签的active类
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));

            // 为当前点击的标签添加active类
            event.target.classList.add('active');

            // 隐藏所有内容区域
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.style.display = 'none');

            // 显示对应的内容区域
            const targetContent = document.getElementById(tabName + '-content');
            if (targetContent) {
                targetContent.style.display = 'block';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示作文点评内容
            document.getElementById('essay-review-content').style.display = 'block';
        });
    </script>
</body>
</html>
