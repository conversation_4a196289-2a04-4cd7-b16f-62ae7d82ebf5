
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>过往作文</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: white;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px 10px;
            font-size: 17px;
            font-weight: 600;
        }

        .time { color: #000; }
        .signal { color: #000; }

        .page-title {
            padding: 0 20px 15px;
            font-size: 24px;
            font-weight: bold;
            color: #999;
        }

        .content {
            padding: 0 20px;
            height: calc(100% - 200px);
            overflow-y: auto;
        }

        /* 筛选区域 */
        .filter-section {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .filter-dropdown {
            flex: 1;
            height: 32px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 0 24px 0 8px;
            font-size: 13px;
            background: white;
            color: #333;
            appearance: none;
            cursor: pointer;
            background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 8"><path fill="%23999" d="M6 8L0 2h12z"/></svg>');
            background-repeat: no-repeat;
            background-position: right 8px center;
            background-size: 12px 6px;
        }

        .filter-dropdown:focus {
            outline: none;
            border-color: #007aff;
        }

        /* 学期信息 */
        .semester-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .semester-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .essay-count {
            font-size: 14px;
            color: #999;
        }

        /* 作文列表 */
        .essay-list {
            margin-bottom: 20px;
        }

        .essay-item {
            background: white;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
        }

        .essay-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .essay-title {
            font-size: 14px;
            color: #666;
        }

        .essay-score {
            font-size: 20px;
            font-weight: bold;
            color: #ff6b6b;
        }

        .essay-content {
            font-size: 16px;
            color: #333;
            margin-bottom: 8px;
        }

        .essay-time {
            font-size: 12px;
            color: #999;
        }

        .arrow-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #ccc;
            font-size: 16px;
        }

        .divider {
            text-align: center;
            color: #ccc;
            font-size: 12px;
            margin: 20px 0;
        }

        /* 底部导航 */
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            margin-bottom: 4px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 首页图标 */
        .nav-item:nth-child(1) .nav-icon::before {
            content: '';
            width: 20px;
            height: 18px;
            border: 2px solid #999;
            border-bottom: none;
            border-radius: 4px 4px 0 0;
            position: relative;
        }

        .nav-item:nth-child(1) .nav-icon::after {
            content: '';
            position: absolute;
            bottom: -2px;
            width: 24px;
            height: 8px;
            border: 2px solid #999;
            border-top: none;
            border-radius: 0 0 2px 2px;
        }

        /* 过往作文图标 */
        .nav-item:nth-child(2) .nav-icon::before {
            content: '';
            width: 16px;
            height: 20px;
            border: 2px solid #999;
            border-radius: 2px;
            position: relative;
        }

        .nav-item:nth-child(2) .nav-icon::after {
            content: '';
            position: absolute;
            top: 6px;
            left: 50%;
            transform: translateX(-50%);
            width: 8px;
            height: 1px;
            background: #999;
            box-shadow: 0 3px 0 #999, 0 6px 0 #999;
        }

        /* 成长数据图标 */
        .nav-item:nth-child(3) .nav-icon::before {
            content: '';
            width: 20px;
            height: 16px;
            border: 2px solid #999;
            border-radius: 2px;
            position: relative;
        }

        .nav-item:nth-child(3) .nav-icon::after {
            content: '';
            position: absolute;
            bottom: 2px;
            left: 3px;
            width: 3px;
            height: 6px;
            background: #999;
            box-shadow: 4px 2px 0 #999, 8px -1px 0 #999, 12px 1px 0 #999;
        }

        /* 个人中心图标 */
        .nav-item:nth-child(4) .nav-icon::before {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid #999;
            border-radius: 50%;
            position: relative;
        }

        .nav-item:nth-child(4) .nav-icon::after {
            content: '';
            position: absolute;
            top: 4px;
            left: 50%;
            transform: translateX(-50%);
            width: 6px;
            height: 6px;
            background: #999;
            border-radius: 50%;
        }

        .nav-text {
            font-size: 12px;
            color: #666;
            transition: color 0.3s ease;
        }

        /* 激活状态 */
        .nav-item.active .nav-icon::before,
        .nav-item.active .nav-icon::after {
            border-color: #667eea;
            background-color: #667eea;
        }

        .nav-item.active .nav-text {
            color: #667eea;
        }

        /* 过往作文激活状态特殊处理 */
        .nav-item.active:nth-child(2) .nav-icon::after {
            background: #667eea;
            box-shadow: 0 3px 0 #667eea, 0 6px 0 #667eea;
        }

        /* 成长数据激活状态特殊处理 */
        .nav-item.active:nth-child(3) .nav-icon::after {
            background: #667eea;
            box-shadow: 4px 2px 0 #667eea, 8px -1px 0 #667eea, 12px 1px 0 #667eea;
        }

        /* 个人中心激活状态特殊处理 */
        .nav-item.active:nth-child(4) .nav-icon::after {
            background: #667eea;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="status-bar">
            <span class="time">9:41</span>
            <span class="signal">📶 📶 📶 🔋</span>
        </div>

        <div class="page-title">历史作文</div>

        <div class="content">
            <!-- 筛选区域 -->
            <div class="filter-section">
                <select class="filter-dropdown">
                    <option>2025~2026学年 上学期</option>
                    <option>2024~2025学年 下学期</option>
                    <option>2024~2025学年 上学期</option>
                </select>
                <select class="filter-dropdown">
                    <option>单元作文</option>
                    <option>期中作文</option>
                    <option>期末作文</option>
                </select>
                <select class="filter-dropdown">
                    <option>记叙文</option>
                    <option>说明文</option>
                    <option>议论文</option>
                </select>
            </div>

            <!-- 学期信息 -->
            <div class="semester-header">
                <div class="semester-title">2025~2026学年上学期</div>
                <div class="essay-count">共8篇</div>
            </div>

            <!-- 作文列表 -->
            <div class="essay-list">
                <div class="essay-item">
                    <div class="essay-header">
                        <div class="essay-title">第二单元 单元作文 全命题</div>
                        <div class="essay-score">28<span style="font-size: 12px;">分</span></div>
                    </div>
                    <div class="essay-content">我的植物朋友</div>
                    <div class="essay-time">2025.09.08 11:22</div>
                    <div class="arrow-icon">›</div>
                </div>

                <div class="essay-item">
                    <div class="essay-header">
                        <div class="essay-title">第一单元 单元作文 全命题</div>
                        <div class="essay-score">28<span style="font-size: 12px;">分</span></div>
                    </div>
                    <div class="essay-content">我的植物朋友</div>
                    <div class="essay-time">2025.09.08 11:22</div>
                    <div class="arrow-icon">›</div>
                </div>
            </div>

            <div class="divider">——— 我是萌萌的分割线 ———</div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item" onclick="goToPage('homepage.html')">
                <div class="nav-icon"></div>
                <div class="nav-text">首页</div>
            </div>
            <div class="nav-item active" onclick="goToPage('past-essays.html')">
                <div class="nav-icon"></div>
                <div class="nav-text">过往作文</div>
            </div>
            <div class="nav-item" onclick="goToPage('growth-data.html')">
                <div class="nav-icon"></div>
                <div class="nav-text">成长数据</div>
            </div>
            <div class="nav-item" onclick="goToPage('profile.html')">
                <div class="nav-icon"></div>
                <div class="nav-text">个人中心</div>
            </div>
        </div>
    </div>

    <script>
        // 底部导航切换和跳转
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                // 移除所有激活状态
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                // 添加当前激活状态
                this.classList.add('active');
            });
        });

        // 页面跳转函数
        function goToPage(url) {
            window.location.href = url;
        }

        // 作文项点击
        document.querySelectorAll('.essay-item').forEach(item => {
            item.addEventListener('click', function() {
                console.log('查看作文详情');
                // 可以跳转到作文详情页
                // window.location.href = 'essay-detail.html';
                alert('查看作文详情');
            });
        });

        // 筛选下拉框变化
        document.querySelectorAll('.filter-dropdown').forEach(dropdown => {
            dropdown.addEventListener('change', function() {
                console.log('筛选条件变化:', this.value);
                // 这里可以添加筛选逻辑
                filterEssays();
            });
        });

        // 筛选功能
        function filterEssays() {
            const semester = document.querySelectorAll('.filter-dropdown')[0].value;
            const type = document.querySelectorAll('.filter-dropdown')[1].value;
            const genre = document.querySelectorAll('.filter-dropdown')[2].value;
            
            console.log('筛选条件:', { semester, type, genre });
            // 实际项目中这里会根据筛选条件更新作文列表
        }
    </script>
</body>
</html>
