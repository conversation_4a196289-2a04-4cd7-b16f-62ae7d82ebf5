<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证您的手机号码</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: white;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px 10px;
            font-size: 17px;
            font-weight: 600;
        }

        .header {
            display: flex;
            align-items: center;
            padding: 10px 20px 20px;
        }

        .back-arrow {
            font-size: 18px;
            color: #999;
            margin-right: 10px;
            cursor: pointer;
        }

        .content {
            padding: 0 20px;
            height: calc(100% - 160px);
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .rocket-icon {
            width: 120px;
            height: 120px;
            margin: 60px 0 40px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .rocket {
            width: 80px;
            height: 100px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 40px 40px 10px 10px;
            position: relative;
            transform: rotate(-15deg);
        }

        .rocket::before {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-top: 20px solid #667eea;
        }

        .rocket::after {
            content: '';
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 30px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 10px;
        }

        .flame {
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 40px;
            background: linear-gradient(180deg, #ff6b6b 0%, #ffa500 50%, #ffff00 100%);
            border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
            animation: flicker 0.5s ease-in-out infinite alternate;
        }

        @keyframes flicker {
            0% { transform: translateX(-50%) scale(1); }
            100% { transform: translateX(-50%) scale(1.1); }
        }

        .main-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
        }

        .subtitle {
            font-size: 16px;
            color: #666;
            line-height: 1.5;
            margin-bottom: 50px;
        }

        .phone-number {
            color: #667eea;
            font-weight: 600;
        }

        .verification-code {
            display: flex;
            gap: 12px;
            margin-bottom: 80px;
        }

        .code-input {
            width: 45px;
            height: 50px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            text-align: center;
            font-size: 20px;
            font-weight: 600;
            color: #333;
            outline: none;
            transition: all 0.3s ease;
        }

        .code-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .code-input.filled {
            border-color: #667eea;
            background-color: #f8f9ff;
        }

        .resend-btn {
            position: fixed;
            bottom: 120px;
            left: 50%;
            transform: translateX(-50%);
            width: 335px;
            height: 50px;
            background: white;
            color: #667eea;
            border: 2px solid #667eea;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .resend-btn:hover {
            background: #667eea;
            color: white;
            transform: translateX(-50%) translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .resend-btn:disabled {
            background: #f5f5f5;
            color: #ccc;
            border-color: #e0e0e0;
            cursor: not-allowed;
            transform: translateX(-50%);
            box-shadow: none;
        }

        .countdown {
            color: #999;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="status-bar">
            <span class="time">9:41</span>
            <span class="signal">📶 📶 📶 🔋</span>
        </div>

        <div class="header">
            <span class="back-arrow" onclick="goBack()">←</span>
        </div>

        <div class="content">
            <div class="rocket-icon">
                <div class="rocket">
                    <div class="flame"></div>
                </div>
            </div>

            <h1 class="main-title">验证您的手机号码</h1>
            <p class="subtitle">
                请输入发送到 <span class="phone-number" id="phoneNumber">138****0000</span> 的6位验证码
            </p>

            <div class="verification-code">
                <input type="text" class="code-input" maxlength="1" id="code1">
                <input type="text" class="code-input" maxlength="1" id="code2">
                <input type="text" class="code-input" maxlength="1" id="code3">
                <input type="text" class="code-input" maxlength="1" id="code4">
                <input type="text" class="code-input" maxlength="1" id="code5">
                <input type="text" class="code-input" maxlength="1" id="code6">
            </div>
        </div>

        <button class="resend-btn" id="resendBtn" onclick="resendCode()">
            <span id="resendText">重新发送验证码</span>
        </button>
    </div>

    <script>
        let countdown = 60;
        let countdownTimer = null;

        // 返回上一页
        function goBack() {
            window.history.back();
        }

        // 获取URL参数中的手机号码
        function getPhoneFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const phone = urlParams.get('phone');
            if (phone) {
                // 格式化手机号显示
                const maskedPhone = phone.substring(0, 3) + '****' + phone.substring(7);
                document.getElementById('phoneNumber').textContent = maskedPhone;
            }
        }

        // 重新发送验证码
        function resendCode() {
            if (countdown > 0) return;
            
            // 重置倒计时
            countdown = 60;
            startCountdown();
            
            // 这里可以添加重新发送验证码的API调用
            console.log('重新发送验证码');
        }

        // 开始倒计时
        function startCountdown() {
            const resendBtn = document.getElementById('resendBtn');
            const resendText = document.getElementById('resendText');
            
            resendBtn.disabled = true;
            
            countdownTimer = setInterval(() => {
                if (countdown > 0) {
                    resendText.innerHTML = `重新发送验证码 <span class="countdown">(${countdown}s)</span>`;
                    countdown--;
                } else {
                    resendText.textContent = '重新发送验证码';
                    resendBtn.disabled = false;
                    clearInterval(countdownTimer);
                }
            }, 1000);
        }

        // 验证码输入处理
        function setupCodeInputs() {
            const inputs = document.querySelectorAll('.code-input');
            
            inputs.forEach((input, index) => {
                input.addEventListener('input', function(e) {
                    // 只允许数字
                    this.value = this.value.replace(/[^\d]/g, '');
                    
                    if (this.value) {
                        this.classList.add('filled');
                        // 自动跳转到下一个输入框
                        if (index < inputs.length - 1) {
                            inputs[index + 1].focus();
                        }
                    } else {
                        this.classList.remove('filled');
                    }
                    
                    // 检查是否所有输入框都已填写
                    checkAllInputsFilled();
                });
                
                input.addEventListener('keydown', function(e) {
                    // 退格键处理
                    if (e.key === 'Backspace' && !this.value && index > 0) {
                        inputs[index - 1].focus();
                        inputs[index - 1].value = '';
                        inputs[index - 1].classList.remove('filled');
                    }
                });
                
                input.addEventListener('paste', function(e) {
                    e.preventDefault();
                    const paste = (e.clipboardData || window.clipboardData).getData('text');
                    const numbers = paste.replace(/[^\d]/g, '').substring(0, 6);
                    
                    for (let i = 0; i < numbers.length && i < inputs.length; i++) {
                        inputs[i].value = numbers[i];
                        inputs[i].classList.add('filled');
                    }
                    
                    checkAllInputsFilled();
                });
            });
        }

        // 检查所有输入框是否都已填写
        function checkAllInputsFilled() {
            const inputs = document.querySelectorAll('.code-input');
            const allFilled = Array.from(inputs).every(input => input.value.length === 1);
            
            if (allFilled) {
                // 获取完整验证码
                const code = Array.from(inputs).map(input => input.value).join('');
                console.log('验证码:', code);
                
                // 这里可以添加验证码验证的逻辑
                setTimeout(() => {
                    alert('验证成功！');
                    // 可以跳转到下一个页面或返回
                    // window.location.href = 'profile.html';
                }, 500);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            getPhoneFromUrl();
            setupCodeInputs();
            startCountdown();
            
            // 自动聚焦第一个输入框
            document.getElementById('code1').focus();
        });
    </script>
</body>
</html>
