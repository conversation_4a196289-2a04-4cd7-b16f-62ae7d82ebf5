

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成长数据</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: white;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px 10px;
            font-size: 17px;
            font-weight: 600;
        }

        .page-title {
            padding: 0 20px 15px;
            font-size: 24px;
            font-weight: bold;
            color: #999;
        }

        .content {
            padding: 0 20px;
            height: calc(100% - 200px);
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .coming-soon {
            text-align: center;
            color: #999;
        }

        .icon {
            font-size: 60px;
            margin-bottom: 20px;
        }

        .title {
            font-size: 20px;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 14px;
        }

        /* 底部导航样式（与其他页面保持一致） */
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            margin-bottom: 4px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 导航图标样式 */
        .nav-item:nth-child(1) .nav-icon::before {
            content: '';
            width: 20px;
            height: 18px;
            border: 2px solid #999;
            border-bottom: none;
            border-radius: 4px 4px 0 0;
            position: relative;
        }

        .nav-item:nth-child(1) .nav-icon::after {
            content: '';
            position: absolute;
            bottom: -2px;
            width: 24px;
            height: 8px;
            border: 2px solid #999;
            border-top: none;
            border-radius: 0 0 2px 2px;
        }

        .nav-item:nth-child(2) .nav-icon::before {
            content: '';
            width: 16px;
            height: 20px;
            border: 2px solid #999;
            border-radius: 2px;
            position: relative;
        }

        .nav-item:nth-child(2) .nav-icon::after {
            content: '';
            position: absolute;
            top: 6px;
            left: 50%;
            transform: translateX(-50%);
            width: 8px;
            height: 1px;
            background: #999;
            box-shadow: 0 3px 0 #999, 0 6px 0 #999;
        }

        .nav-item:nth-child(3) .nav-icon::before {
            content: '';
            width: 20px;
            height: 16px;
            border: 2px solid #999;
            border-radius: 2px;
            position: relative;
        }

        .nav-item:nth-child(3) .nav-icon::after {
            content: '';
            position: absolute;
            bottom: 2px;
            left: 3px;
            width: 3px;
            height: 6px;
            background: #999;
            box-shadow: 4px 2px 0 #999, 8px -1px 0 #999, 12px 1px 0 #999;
        }

        .nav-item:nth-child(4) .nav-icon::before {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid #999;
            border-radius: 50%;
            position: relative;
        }

        .nav-item:nth-child(4) .nav-icon::after {
            content: '';
            position: absolute;
            top: 4px;
            left: 50%;
            transform: translateX(-50%);
            width: 6px;
            height: 6px;
            background: #999;
            border-radius: 50%;
        }

        .nav-text {
            font-size: 12px;
            color: #666;
            transition: color 0.3s ease;
        }

        .nav-item.active .nav-icon::before,
        .nav-item.active .nav-icon::after {
            border-color: #667eea;
            background-color: #667eea;
        }

        .nav-item.active .nav-text {
            color: #667eea;
        }

        .nav-item.active:nth-child(3) .nav-icon::after {
            background: #667eea;
            box-shadow: 4px 2px 0 #667eea, 8px -1px 0 #667eea, 12px 1px 0 #667eea;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="status-bar">
            <span class="time">9:41</span>
            <span class="signal">📶 📶 📶 🔋</span>
        </div>

        <div class="page-title">成长数据</div>

        <div class="content">
            <div class="coming-soon">
                <div class="icon">📊</div>
                <div class="title">成长数据</div>
                <div class="subtitle">功能开发中，敬请期待...</div>
            </div>
        </div>

        <div class="bottom-nav">
            <div class="nav-item" onclick="goToPage('homepage.html')">
                <div class="nav-icon"></div>
                <div class="nav-text">首页</div>
            </div>
            <div class="nav-item" onclick="goToPage('past-essays.html')">
                <div class="nav-icon"></div>
                <div class="nav-text">过往作文</div>
            </div>
            <div class="nav-item active">
                <div class="nav-icon"></div>
                <div class="nav-text">成长数据</div>
            </div>
            <div class="nav-item" onclick="goToPage('profile.html')">
                <div class="nav-icon"></div>
                <div class="nav-text">个人中心</div>
            </div>
        </div>
    </div>

    <script>
        function goToPage(url) {
            window.location.href = url;
        }
    </script>
</body>
</html>