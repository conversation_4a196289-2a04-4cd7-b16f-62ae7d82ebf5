<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作文要求</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: white;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px 10px;
            font-size: 17px;
            font-weight: 600;
        }

        .time { color: #000; }
        .signal { color: #000; }

        /* 顶部导航栏 */
        .header {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .back-btn {
            font-size: 18px;
            color: #333;
            cursor: pointer;
            margin-right: 15px;
        }

        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        /* 标签区域 */
        .tabs {
            display: flex;
            padding: 15px 20px 0;
            gap: 20px;
        }

        .tab {
            font-size: 16px;
            color: #666;
            padding-bottom: 10px;
            border-bottom: 2px solid transparent;
            cursor: pointer;
        }

        .tab.active {
            color: #333;
            border-bottom-color: #667eea;
        }

        /* 内容区域 */
        .content {
            padding: 20px;
            height: calc(100% - 200px);
            overflow-y: auto;
        }

        /* 作文信息 */
        .essay-info {
            margin-bottom: 30px;
        }

        .info-row {
            display: flex;
            margin-bottom: 15px;
            align-items: flex-start;
        }

        .info-label {
            font-size: 16px;
            color: #333;
            width: 80px;
            flex-shrink: 0;
        }

        .info-value {
            font-size: 16px;
            color: #333;
            flex: 1;
            line-height: 1.5;
        }

        .essay-requirements {
            line-height: 1.6;
            color: #333;
        }

        /* 上传作文区域 */
        .upload-section {
            margin-bottom: 30px;
        }

        .upload-title {
            font-size: 16px;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .info-icon {
            width: 16px;
            height: 16px;
            background: #ccc;
            border-radius: 50%;
            margin-left: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
        }

        .upload-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .upload-item {
            aspect-ratio: 1.4;
            border: 2px dashed #ddd;
            border-radius: 8px;
            background: #f9f9f9;
            position: relative;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .upload-item::before {
            content: '✕';
            position: absolute;
            top: -8px;
            right: -8px;
            width: 20px;
            height: 20px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        .add-upload {
            border: 2px dashed #ccc;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #999;
        }

        .add-upload::before {
            content: '+';
            background: none;
            color: #999;
            position: static;
            width: auto;
            height: auto;
            border-radius: 0;
        }

        /* 底部提交按钮 */
        .submit-section {
            position: absolute;
            bottom: 30px;
            left: 20px;
            right: 20px;
        }

        .submit-btn {
            width: 100%;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="status-bar">
            <span class="time">9:41</span>
            <span class="signal">📶 📶 📶 🔋</span>
        </div>

        <!-- 顶部导航栏 -->
        <div class="header">
            <div class="back-btn" onclick="goBack()">←</div>
            <div class="header-title">作文要求</div>
        </div>

        <!-- 标签区域 -->
        <div class="tabs">
            <div class="tab active">第三单元</div>
            <div class="tab">单元作文</div>
            <div class="tab">全命题</div>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 作文信息 -->
            <div class="essay-info">
                <div class="info-row">
                    <div class="info-label">作文命题</div>
                    <div class="info-value">我的植物朋友</div>
                </div>
                <div class="info-row">
                    <div class="info-label">字数要求</div>
                    <div class="info-value">300字</div>
                </div>
                <div class="info-row">
                    <div class="info-label">总分</div>
                    <div class="info-value">30分</div>
                </div>
                <div class="info-row">
                    <div class="info-label">作文要求</div>
                    <div class="info-value essay-requirements">
                        选择一项自己观察的小植物（可以是教学楼上，也可以是自己在家观察的），再用你学到的观察方法，一定要结合你的观察字卡，重点把你观察到的写清楚，可以用上"……接着……然后……最后……"等表示顺序的词语。
                    </div>
                </div>
            </div>

            <!-- 上传作文区域 -->
            <div class="upload-section">
                <div class="upload-title">
                    上传作文
                    <div class="info-icon">i</div>
                </div>
                <div class="upload-grid">
                    <div class="upload-item"></div>
                    <div class="upload-item"></div>
                    <div class="upload-item"></div>
                    <div class="upload-item"></div>
                </div>
                <div class="add-upload"></div>
            </div>
        </div>

        <!-- 底部提交按钮 -->
        <div class="submit-section">
            <button class="submit-btn" onclick="submitEssay()">提交</button>
        </div>
    </div>

    <script>
        // 返回按钮
        function goBack() {
            window.location.href = 'homepage.html';
        }

        // 标签切换
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 上传图片
        document.querySelectorAll('.upload-item, .add-upload').forEach(item => {
            item.addEventListener('click', function() {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = 'image/*';
                input.multiple = true;
                input.click();
                
                input.addEventListener('change', function(e) {
                    console.log('选择了文件:', e.target.files);
                    // 这里可以添加文件上传逻辑
                });
            });
        });

        // 提交作文
        function submitEssay() {
            alert('作文提交成功！');
            // 这里可以添加提交逻辑
        }
    </script>
</body>
</html>
