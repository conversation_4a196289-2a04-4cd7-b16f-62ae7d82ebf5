<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加家庭成员</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: white;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px 10px;
            font-size: 17px;
            font-weight: 600;
        }

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 20px 20px;
        }

        .back-arrow {
            font-size: 18px;
            color: #999;
            cursor: pointer;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .content {
            padding: 0 20px;
            height: calc(100% - 160px);
            overflow-y: auto;
        }

        /* 成人表单样式 */
        .adult-form {
            display: none;
        }

        .adult-form.active {
            display: block;
        }

        /* 学生表单样式 */
        .student-form {
            display: none;
        }

        .student-form.active {
            display: block;
        }

        .avatar-section {
            display: flex;
            justify-content: center;
            margin: 30px 0 40px;
        }

        .avatar-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .avatar-icon {
            color: white;
            font-size: 32px;
        }

        .form-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 18px 0;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
        }

        .form-item:last-child {
            border-bottom: none;
        }

        .form-label {
            font-size: 16px;
            color: #333;
        }

        .form-value {
            display: flex;
            align-items: center;
            color: #999;
            font-size: 16px;
        }

        .form-value.has-value {
            color: #667eea;
        }

        .form-value input {
            border: none;
            outline: none;
            background: transparent;
            color: #333;
            font-size: 16px;
            text-align: right;
            width: 150px;
        }

        .form-value input::placeholder {
            color: #999;
        }

        .form-arrow {
            margin-left: 8px;
            color: #999;
            font-size: 14px;
        }

        /* 简单表单样式（原有的） */
        .simple-form {
            margin-top: 40px;
        }

        .input-group {
            margin-bottom: 30px;
        }

        .input-label {
            font-size: 16px;
            color: #333;
            margin-bottom: 12px;
            display: block;
        }

        .input-field {
            width: 100%;
            height: 50px;
            border: none;
            border-bottom: 1px solid #e0e0e0;
            background: transparent;
            font-size: 16px;
            color: #333;
            outline: none;
            padding: 0;
        }

        .input-field::placeholder {
            color: #999;
            font-size: 16px;
        }

        .input-field:focus {
            border-bottom-color: #667eea;
        }

        .relation-display {
            width: 100%;
            height: 50px;
            border: none;
            border-bottom: 1px solid #e0e0e0;
            background: transparent;
            font-size: 16px;
            color: #667eea;
            outline: none;
            padding: 0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .save-btn {
            position: fixed;
            bottom: 120px;
            left: 50%;
            transform: translateX(-50%);
            width: 335px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .save-btn:hover {
            transform: translateX(-50%) translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .save-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: translateX(-50%);
            box-shadow: none;
        }

        /* 性别选择弹出层样式 */
        .gender-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .gender-popup {
            width: 300px;
            background: white;
            border-radius: 15px;
            padding: 30px 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .gender-title {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 30px;
        }

        .gender-options {
            display: flex;
            flex-direction: column;
            gap: 1px;
        }

        .gender-option {
            display: flex;
            align-items: center;
            padding: 20px;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            font-size: 16px;
            color: #333;
        }

        .gender-option:first-child {
            border-radius: 10px 10px 0 0;
        }

        .gender-option:last-child {
            border-radius: 0 0 10px 10px;
        }

        .gender-option:hover {
            background: #e9ecef;
        }

        .gender-icon {
            width: 24px;
            height: 24px;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .gender-icon.male {
            color: #4285f4;
        }

        .gender-icon.female {
            color: #ea4335;
        }

        .gender-text {
            flex: 1;
            text-align: left;
        }

        /* 日期选择器样式 */
        .date-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .date-popup {
            width: 320px;
            height: 450px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            display: flex;
            flex-direction: column;
        }

        .date-title {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }

        .date-picker-container {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: stretch;
            gap: 1px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
        }

        .date-column {
            flex: 1;
            height: 320px;
            position: relative;
            overflow: hidden;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
        }

        .date-column:first-child {
            border-radius: 10px 0 0 10px;
        }

        .date-column:last-child {
            border-radius: 0 10px 10px 0;
        }

        .date-column-header {
            text-align: center;
            font-size: 14px;
            color: #666;
            padding: 12px 0;
            background: #f0f0f0;
            font-weight: 500;
            border-bottom: 1px solid #e0e0e0;
        }

        .date-list {
            flex: 1;
            overflow-y: auto;
            padding: 0;
            margin: 0;
            list-style: none;
            scroll-behavior: smooth;
        }

        .date-list::-webkit-scrollbar {
            width: 6px;
        }

        .date-list::-webkit-scrollbar-track {
            background: #f8f9fa;
        }

        .date-list::-webkit-scrollbar-thumb {
            background: #ddd;
            border-radius: 3px;
        }

        .date-list::-webkit-scrollbar-thumb:hover {
            background: #bbb;
        }

        .date-item {
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: #666;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 1px solid #f0f0f0;
            background: white;
        }

        .date-item:last-child {
            border-bottom: none;
        }

        .date-item:hover {
            background: #f0f0f0;
            color: #333;
        }

        .date-item.selected {
            background: #667eea;
            color: white;
            font-weight: 600;
        }

        .date-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .date-btn {
            flex: 1;
            height: 44px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .date-btn-cancel {
            background: #f8f9fa;
            color: #666;
        }

        .date-btn-cancel:hover {
            background: #e9ecef;
        }

        .date-btn-confirm {
            background: #667eea;
            color: white;
        }

        .date-btn-confirm:hover {
            background: #5a6fd8;
        }

        /* 年级班级选择弹出层样式 */
        .grade-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .grade-popup {
            width: 320px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            max-height: 80vh;
            overflow-y: auto;
        }

        .grade-title {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }

        .grade-section {
            margin-bottom: 30px;
        }

        .grade-section:last-child {
            margin-bottom: 0;
        }

        .grade-section-title {
            font-size: 16px;
            color: #666;
            margin-bottom: 15px;
            padding: 10px 15px;
            background: #f8f9fa;
            border-radius: 8px;
            text-align: center;
        }

        .grade-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .grade-table th {
            background: #f5f5f5;
            color: #333;
            font-weight: 600;
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #e0e0e0;
            font-size: 14px;
        }

        .grade-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            color: #333;
        }

        .grade-table td:hover {
            background: #f0f0f0;
        }

        .grade-table tr:last-child td {
            border-bottom: none;
        }

        .grade-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .grade-btn {
            flex: 1;
            height: 44px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .grade-btn-cancel {
            background: #f8f9fa;
            color: #666;
        }

        .grade-btn-cancel:hover {
            background: #e9ecef;
        }

        /* 身份选择弹出层样式 */
        .identity-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .identity-popup {
            width: 320px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            max-height: 80vh;
            overflow-y: auto;
        }

        .identity-title {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }

        .identity-section {
            margin-bottom: 30px;
        }

        .identity-section:last-child {
            margin-bottom: 0;
        }

        .identity-section-title {
            font-size: 16px;
            color: #666;
            margin-bottom: 15px;
            padding: 10px 15px;
            background: #f8f9fa;
            border-radius: 8px;
            text-align: center;
        }

        .identity-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .identity-table th {
            background: #f5f5f5;
            color: #333;
            font-weight: 600;
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #e0e0e0;
            font-size: 14px;
        }

        .identity-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            color: #333;
        }

        .identity-table td:hover {
            background: #f0f0f0;
        }

        .identity-table tr:last-child td {
            border-bottom: none;
        }

        .identity-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .identity-btn {
            flex: 1;
            height: 44px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .identity-btn-cancel {
            background: #f8f9fa;
            color: #666;
        }

        .identity-btn-cancel:hover {
            background: #e9ecef;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="status-bar">
            <span class="time">9:41</span>
            <span class="signal">📶 📶 📶 🔋</span>
        </div>

        <div class="header">
            <span class="back-arrow" onclick="goBack()">←</span>
            <span class="page-title">添加家庭成员</span>
        </div>

        <div class="content">
            <!-- 成人表单 -->
            <div class="adult-form" id="adultForm">
                <div class="avatar-section">
                    <div class="avatar-circle" onclick="selectAvatar()">
                        <span class="avatar-icon">👤</span>
                    </div>
                </div>

                <div class="form-item" onclick="selectRelation()">
                    <span class="form-label">成员关系</span>
                    <div class="form-value has-value" id="adultRelationValue">
                        爸爸
                        <span class="form-arrow">›</span>
                    </div>
                </div>

                <div class="form-item">
                    <span class="form-label">名字</span>
                    <div class="form-value">
                        <input type="text" id="adultName" placeholder="请输入">
                    </div>
                </div>

                <div class="form-item" onclick="selectGender()">
                    <span class="form-label">性别</span>
                    <div class="form-value" id="genderValue">
                        请选择
                        <span class="form-arrow">›</span>
                    </div>
                </div>

                <div class="form-item" onclick="selectBirthDate()">
                    <span class="form-label">出生年月</span>
                    <div class="form-value" id="birthDateValue">
                        请选择
                        <span class="form-arrow">›</span>
                    </div>
                </div>

                <div class="form-item" onclick="selectPhone()">
                    <span class="form-label">手机号</span>
                    <div class="form-value">
                        <input type="tel" id="adultPhone" placeholder="请输入" maxlength="11">
                    </div>
                </div>
            </div>

            <!-- 学生表单 -->
            <div class="student-form" id="studentForm">
                <div class="avatar-section">
                    <div class="avatar-circle" onclick="selectAvatar()">
                        <span class="avatar-icon">👤</span>
                    </div>
                </div>

                <div class="form-item" onclick="selectRelation()">
                    <span class="form-label">成员关系</span>
                    <div class="form-value has-value" id="studentRelationValue">
                        学员
                        <span class="form-arrow">›</span>
                    </div>
                </div>

                <div class="form-item">
                    <span class="form-label">姓名</span>
                    <div class="form-value">
                        <input type="text" id="studentName" placeholder="请输入">
                    </div>
                </div>

                <div class="form-item" onclick="selectStudentGender()">
                    <span class="form-label">性别</span>
                    <div class="form-value" id="studentGenderValue">
                        请选择
                        <span class="form-arrow">›</span>
                    </div>
                </div>

                <div class="form-item" onclick="selectStudentBirthDate()">
                    <span class="form-label">出生年月</span>
                    <div class="form-value" id="studentBirthDateValue">
                        请选择
                        <span class="form-arrow">›</span>
                    </div>
                </div>

                <div class="form-item">
                    <span class="form-label">就读学校</span>
                    <div class="form-value">
                        <input type="text" id="studentSchool" placeholder="请输入">
                    </div>
                </div>

                <div class="form-item" onclick="selectGradeClass()">
                    <span class="form-label">年级/班级</span>
                    <div class="form-value" id="gradeClassValue">
                        请选择
                        <span class="form-arrow">›</span>
                    </div>
                </div>

                <div class="form-item">
                    <span class="form-label">座号</span>
                    <div class="form-value">
                        <input type="text" id="studentSeatNumber" placeholder="请输入">
                    </div>
                </div>
            </div>

            <!-- 简单表单（原有的） -->
            <div class="simple-form" id="simpleForm">
                <div class="input-group">
                    <label class="input-label">成员关系</label>
                    <div class="relation-display" onclick="selectRelation()">
                        <span id="relationText">请选择成员关系</span>
                        <span>›</span>
                    </div>
                </div>

                <div class="input-group">
                    <label class="input-label">姓名</label>
                    <input type="text" class="input-field" id="memberName" placeholder="请输入成员姓名">
                </div>

                <div class="input-group">
                    <label class="input-label">手机号码</label>
                    <input type="tel" class="input-field" id="memberPhone" placeholder="请输入手机号码" maxlength="11">
                </div>
            </div>
        </div>

        <button class="save-btn" id="saveBtn" onclick="saveMember()">保存</button>
    </div>

    <!-- 性别选择弹出层 -->
    <div class="gender-overlay" id="genderOverlay" onclick="closeGenderPopup(event)">
        <div class="gender-popup" onclick="event.stopPropagation()">
            <div class="gender-title">选择性别</div>
            <div class="gender-options">
                <button class="gender-option" onclick="selectGenderOption('男')">
                    <div class="gender-icon male">♂</div>
                    <div class="gender-text">男</div>
                </button>
                <button class="gender-option" onclick="selectGenderOption('女')">
                    <div class="gender-icon female">♀</div>
                    <div class="gender-text">女</div>
                </button>
            </div>
        </div>
    </div>

    <!-- 日期选择弹出层 -->
    <div class="date-overlay" id="dateOverlay" onclick="closeDatePopup(event)">
        <div class="date-popup" onclick="event.stopPropagation()">
            <div class="date-title">选择出生年月</div>
            <div class="date-picker-container">
                <div class="date-column">
                    <div class="date-column-header">年份</div>
                    <ul class="date-list" id="yearList">
                        <!-- 年份选项将通过JavaScript生成 -->
                    </ul>
                </div>
                <div class="date-column">
                    <div class="date-column-header">月份</div>
                    <ul class="date-list" id="monthList">
                        <!-- 月份选项将通过JavaScript生成 -->
                    </ul>
                </div>
                <div class="date-column">
                    <div class="date-column-header">日期</div>
                    <ul class="date-list" id="dayList">
                        <!-- 日期选项将通过JavaScript生成 -->
                    </ul>
                </div>
            </div>
            <div class="date-buttons">
                <button class="date-btn date-btn-cancel" onclick="closeDatePopup()">取消</button>
                <button class="date-btn date-btn-confirm" onclick="confirmDateSelection()">确定</button>
            </div>
        </div>
    </div>

    <!-- 年级班级选择弹出层 -->
    <div class="grade-overlay" id="gradeOverlay" onclick="closeGradePopup(event)">
        <div class="grade-popup" onclick="event.stopPropagation()">
            <div class="grade-title">选择年级班级</div>
            <div class="grade-section">
                <div class="grade-section-title">选择年级班级</div>
                <table class="grade-table">
                    <thead>
                        <tr>
                            <th>年级</th>
                            <th>班级</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td onclick="selectGradeClassOption('一年级', '1班')">一年级</td>
                            <td onclick="selectGradeClassOption('一年级', '1班')">1班</td>
                        </tr>
                        <tr>
                            <td onclick="selectGradeClassOption('二年级', '2班')">二年级</td>
                            <td onclick="selectGradeClassOption('二年级', '2班')">2班</td>
                        </tr>
                        <tr>
                            <td onclick="selectGradeClassOption('三年级', '3班')">三年级</td>
                            <td onclick="selectGradeClassOption('三年级', '3班')">3班</td>
                        </tr>
                        <tr>
                            <td onclick="selectGradeClassOption('四年级', '4班')">四年级</td>
                            <td onclick="selectGradeClassOption('四年级', '4班')">4班</td>
                        </tr>
                        <tr>
                            <td onclick="selectGradeClassOption('五年级', '5班')">五年级</td>
                            <td onclick="selectGradeClassOption('五年级', '5班')">5班</td>
                        </tr>
                        <tr>
                            <td onclick="selectGradeClassOption('六年级', '6班')">六年级</td>
                            <td onclick="selectGradeClassOption('六年级', '6班')">6班</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="grade-buttons">
                <button class="grade-btn grade-btn-cancel" onclick="closeGradePopup()">取消</button>
            </div>
        </div>
    </div>

    <!-- 身份选择弹出层 -->
    <div class="identity-overlay" id="identityOverlay" onclick="closeIdentityPopup(event)">
        <div class="identity-popup" onclick="event.stopPropagation()">
            <div class="identity-title">编辑身份</div>
            <div class="identity-section">
                <div class="identity-section-title">身份</div>
                <table class="identity-table">
                    <thead>
                        <tr>
                            <th>类型</th>
                            <th>身份</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td onclick="selectIdentityOption('家长', '妈妈')">家长</td>
                            <td onclick="selectIdentityOption('家长', '妈妈')">妈妈</td>
                        </tr>
                        <tr>
                            <td onclick="selectIdentityOption('家长', '爸爸')">家长</td>
                            <td onclick="selectIdentityOption('家长', '爸爸')">爸爸</td>
                        </tr>
                        <tr>
                            <td onclick="selectIdentityOption('成人', '爷爷')">成人</td>
                            <td onclick="selectIdentityOption('成人', '爷爷')">爷爷</td>
                        </tr>
                        <tr>
                            <td onclick="selectIdentityOption('学生', '学生')">学生</td>
                            <td onclick="selectIdentityOption('学生', '学生')">学生</td>
                        </tr>
                        <tr>
                            <td onclick="selectIdentityOption('其他', '外公')">其他</td>
                            <td onclick="selectIdentityOption('其他', '外公')">外公</td>
                        </tr>
                        <tr>
                            <td onclick="selectIdentityOption('其他', '外婆')">其他</td>
                            <td onclick="selectIdentityOption('其他', '外婆')">外婆</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="identity-buttons">
                <button class="identity-btn identity-btn-cancel" onclick="closeIdentityPopup()">取消</button>
            </div>
        </div>
    </div>

    <script>
        let selectedRelation = '';
        let selectedGender = '';
        let selectedBirthDate = '';
        let selectedGradeClass = '';
        let isAdultMember = false;
        let isStudentMember = false;

        // 成人关系列表
        const adultRelations = ['妈妈', '爸爸', '爷爷', '奶奶', '外公', '外婆'];

        // 学生关系列表
        const studentRelations = ['宝贝', '哥哥', '姐姐', '弟弟', '妹妹'];

        // 返回上一页
        function goBack() {
            window.history.back();
        }

        // 选择成员关系
        function selectRelation() {
            showIdentityPopup();
        }

        // 选择头像
        function selectAvatar() {
            alert('选择头像功能开发中');
        }

        // 选择性别
        function selectGender() {
            showGenderPopup('adult');
        }

        // 选择出生年月
        function selectBirthDate() {
            showDatePicker('adult');
        }

        // 选择手机号（实际上是聚焦到输入框）
        function selectPhone() {
            document.getElementById('adultPhone').focus();
        }

        // 学生表单相关函数
        // 选择学生性别
        function selectStudentGender() {
            showGenderPopup('student');
        }

        // 选择学生出生年月
        function selectStudentBirthDate() {
            showDatePicker('student');
        }

        // 选择年级/班级
        function selectGradeClass() {
            showGradePopup();
        }

        // 显示年级班级选择弹出层
        function showGradePopup() {
            document.getElementById('gradeOverlay').style.display = 'flex';
        }

        // 关闭年级班级选择弹出层
        function closeGradePopup(event) {
            if (event && event.target === document.getElementById('gradeOverlay')) {
                document.getElementById('gradeOverlay').style.display = 'none';
            } else if (!event) {
                document.getElementById('gradeOverlay').style.display = 'none';
            }
        }

        // 选择年级班级选项
        function selectGradeClassOption(grade, classNum) {
            selectedGradeClass = `${grade}${classNum}`;
            document.getElementById('gradeClassValue').innerHTML = `${grade}${classNum} <span class="form-arrow">›</span>`;
            document.getElementById('gradeClassValue').classList.add('has-value');
            closeGradePopup();
        }

        // 显示身份选择弹出层
        function showIdentityPopup() {
            document.getElementById('identityOverlay').style.display = 'flex';
        }

        // 关闭身份选择弹出层
        function closeIdentityPopup(event) {
            if (event && event.target === document.getElementById('identityOverlay')) {
                document.getElementById('identityOverlay').style.display = 'none';
            } else if (!event) {
                document.getElementById('identityOverlay').style.display = 'none';
            }
        }

        // 选择身份选项
        function selectIdentityOption(type, identity) {
            selectedRelation = identity;

            // 根据选择的身份决定显示哪个表单
            if (['妈妈', '爸爸', '爷爷', '奶奶', '外公', '外婆'].includes(identity)) {
                // 成人表单
                isAdultMember = true;
                isStudentMember = false;
                document.getElementById('adultForm').classList.add('active');
                document.getElementById('studentForm').classList.remove('active');
                document.getElementById('simpleForm').style.display = 'none';
                document.getElementById('adultRelationValue').innerHTML = `${identity} <span class="form-arrow">›</span>`;
                document.querySelector('.page-title').textContent = '添加成员';
            } else if (identity === '学生') {
                // 学生表单
                isStudentMember = true;
                isAdultMember = false;
                document.getElementById('studentForm').classList.add('active');
                document.getElementById('adultForm').classList.remove('active');
                document.getElementById('simpleForm').style.display = 'none';
                document.getElementById('studentRelationValue').innerHTML = `学员 <span class="form-arrow">›</span>`;
                document.querySelector('.page-title').textContent = '添加成员';
            } else {
                // 简单表单
                isAdultMember = false;
                isStudentMember = false;
                document.getElementById('simpleForm').style.display = 'block';
                document.getElementById('adultForm').classList.remove('active');
                document.getElementById('studentForm').classList.remove('active');
                document.getElementById('relationText').textContent = identity;
                document.getElementById('relationText').style.color = '#667eea';
            }

            closeIdentityPopup();
        }

        // 性别选择弹出层相关函数
        let currentGenderType = ''; // 'adult' 或 'student'

        function showGenderPopup(type) {
            currentGenderType = type;
            document.getElementById('genderOverlay').style.display = 'flex';
        }

        function closeGenderPopup(event) {
            if (event.target === document.getElementById('genderOverlay')) {
                document.getElementById('genderOverlay').style.display = 'none';
            }
        }

        function selectGenderOption(gender) {
            selectedGender = gender;

            if (currentGenderType === 'adult') {
                document.getElementById('genderValue').innerHTML = `${gender} <span class="form-arrow">›</span>`;
                document.getElementById('genderValue').classList.add('has-value');
            } else if (currentGenderType === 'student') {
                document.getElementById('studentGenderValue').innerHTML = `${gender} <span class="form-arrow">›</span>`;
                document.getElementById('studentGenderValue').classList.add('has-value');
            }

            document.getElementById('genderOverlay').style.display = 'none';
        }

        // 日期选择器相关函数
        let currentDateType = ''; // 'adult' 或 'student'
        let selectedYear = new Date().getFullYear();
        let selectedMonth = new Date().getMonth() + 1;
        let selectedDay = new Date().getDate();

        function showDatePicker(type) {
            currentDateType = type;
            initializeDatePicker();
            document.getElementById('dateOverlay').style.display = 'flex';
        }

        function closeDatePopup(event) {
            if (event && event.target === document.getElementById('dateOverlay')) {
                document.getElementById('dateOverlay').style.display = 'none';
            } else if (!event) {
                document.getElementById('dateOverlay').style.display = 'none';
            }
        }

        function initializeDatePicker() {
            // 生成年份列表 (1950-2024)
            const yearList = document.getElementById('yearList');
            yearList.innerHTML = '';
            for (let year = 2024; year >= 1950; year--) {
                const li = document.createElement('li');
                li.className = 'date-item';
                li.textContent = year;
                li.onclick = () => selectYear(year);
                if (year === selectedYear) {
                    li.classList.add('selected');
                }
                yearList.appendChild(li);
            }

            // 生成月份列表
            const monthList = document.getElementById('monthList');
            monthList.innerHTML = '';
            for (let month = 1; month <= 12; month++) {
                const li = document.createElement('li');
                li.className = 'date-item';
                li.textContent = month.toString().padStart(2, '0');
                li.onclick = () => selectMonth(month);
                if (month === selectedMonth) {
                    li.classList.add('selected');
                }
                monthList.appendChild(li);
            }

            // 生成日期列表
            updateDayList();
        }

        function selectYear(year) {
            selectedYear = year;
            updateDateSelection('year');
            updateDayList(); // 更新日期列表，因为年份变化可能影响闰年
        }

        function selectMonth(month) {
            selectedMonth = month;
            updateDateSelection('month');
            updateDayList(); // 更新日期列表，因为月份变化影响天数
        }

        function selectDay(day) {
            selectedDay = day;
            updateDateSelection('day');
        }

        function updateDateSelection(type) {
            // 清除之前的选中状态
            if (type === 'year') {
                document.querySelectorAll('#yearList .date-item').forEach(item => {
                    item.classList.remove('selected');
                    if (parseInt(item.textContent) === selectedYear) {
                        item.classList.add('selected');
                    }
                });
            } else if (type === 'month') {
                document.querySelectorAll('#monthList .date-item').forEach(item => {
                    item.classList.remove('selected');
                    if (parseInt(item.textContent) === selectedMonth) {
                        item.classList.add('selected');
                    }
                });
            } else if (type === 'day') {
                document.querySelectorAll('#dayList .date-item').forEach(item => {
                    item.classList.remove('selected');
                    if (parseInt(item.textContent) === selectedDay) {
                        item.classList.add('selected');
                    }
                });
            }
        }

        function updateDayList() {
            const dayList = document.getElementById('dayList');
            dayList.innerHTML = '';

            // 获取当前选中年月的天数
            const daysInMonth = new Date(selectedYear, selectedMonth, 0).getDate();

            for (let day = 1; day <= daysInMonth; day++) {
                const li = document.createElement('li');
                li.className = 'date-item';
                li.textContent = day.toString().padStart(2, '0');
                li.onclick = () => selectDay(day);
                if (day === selectedDay && selectedDay <= daysInMonth) {
                    li.classList.add('selected');
                }
                dayList.appendChild(li);
            }

            // 如果当前选中的日期超过了该月的天数，调整为该月最后一天
            if (selectedDay > daysInMonth) {
                selectedDay = daysInMonth;
                updateDateSelection('day');
            }
        }

        function confirmDateSelection() {
            const formattedDate = `${selectedYear}.${selectedMonth.toString().padStart(2, '0')}.${selectedDay.toString().padStart(2, '0')}`;
            selectedBirthDate = formattedDate;

            if (currentDateType === 'adult') {
                document.getElementById('birthDateValue').innerHTML = `${formattedDate} <span class="form-arrow">›</span>`;
                document.getElementById('birthDateValue').classList.add('has-value');
            } else if (currentDateType === 'student') {
                document.getElementById('studentBirthDateValue').innerHTML = `${formattedDate} <span class="form-arrow">›</span>`;
                document.getElementById('studentBirthDateValue').classList.add('has-value');
            }

            closeDatePopup();
        }

        // 保存成员
        function saveMember() {
            if (isAdultMember) {
                // 成人表单验证
                const name = document.getElementById('adultName').value.trim();
                const phone = document.getElementById('adultPhone').value.trim();

                if (!selectedRelation) {
                    alert('请选择成员关系');
                    return;
                }

                if (!name) {
                    alert('请输入名字');
                    return;
                }

                if (!selectedGender) {
                    alert('请选择性别');
                    return;
                }

                if (!selectedBirthDate) {
                    alert('请选择出生年月');
                    return;
                }

                if (!phone) {
                    alert('请输入手机号');
                    return;
                }

                // 验证手机号格式
                const phoneRegex = /^1[3-9]\d{9}$/;
                if (!phoneRegex.test(phone)) {
                    alert('手机号码格式不正确');
                    return;
                }

                alert(`成功添加${selectedRelation}：${name}`);
            } else if (isStudentMember) {
                // 学生表单验证
                const name = document.getElementById('studentName').value.trim();
                const school = document.getElementById('studentSchool').value.trim();
                const seatNumber = document.getElementById('studentSeatNumber').value.trim();

                if (!selectedRelation) {
                    alert('请选择成员关系');
                    return;
                }

                if (!name) {
                    alert('请输入姓名');
                    return;
                }

                if (!selectedGender) {
                    alert('请选择性别');
                    return;
                }

                if (!selectedBirthDate) {
                    alert('请选择出生年月');
                    return;
                }

                if (!school) {
                    alert('请输入就读学校');
                    return;
                }

                if (!selectedGradeClass) {
                    alert('请选择年级/班级');
                    return;
                }

                if (!seatNumber) {
                    alert('请输入座号');
                    return;
                }

                alert(`成功添加学员：${name}`);
            } else {
                // 简单表单验证
                const name = document.getElementById('memberName').value.trim();
                const phone = document.getElementById('memberPhone').value.trim();

                if (!selectedRelation) {
                    alert('请选择成员关系');
                    return;
                }

                if (!name) {
                    alert('请输入成员姓名');
                    return;
                }

                if (!phone) {
                    alert('请输入手机号码');
                    return;
                }

                // 验证手机号格式
                const phoneRegex = /^1[3-9]\d{9}$/;
                if (!phoneRegex.test(phone)) {
                    alert('手机号码格式不正确');
                    return;
                }

                alert(`成功添加${selectedRelation}：${name}`);
            }

            // 返回个人中心页面
            window.location.href = 'profile.html';
        }

        // 获取URL参数中的关系
        function getRelationFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            const relation = urlParams.get('relation');
            if (relation) {
                selectedRelation = relation;

                // 判断是否为成人关系
                isAdultMember = adultRelations.includes(relation);
                // 判断是否为学生关系
                isStudentMember = studentRelations.includes(relation);

                if (isAdultMember) {
                    // 显示成人表单
                    document.getElementById('adultForm').classList.add('active');
                    document.getElementById('studentForm').classList.remove('active');
                    document.getElementById('simpleForm').style.display = 'none';
                    document.getElementById('adultRelationValue').innerHTML = `${relation} <span class="form-arrow">›</span>`;
                    document.querySelector('.page-title').textContent = '添加成员';
                } else if (isStudentMember) {
                    // 显示学生表单
                    document.getElementById('studentForm').classList.add('active');
                    document.getElementById('adultForm').classList.remove('active');
                    document.getElementById('simpleForm').style.display = 'none';
                    document.getElementById('studentRelationValue').innerHTML = `学员 <span class="form-arrow">›</span>`;
                    document.querySelector('.page-title').textContent = '添加成员';
                } else {
                    // 显示简单表单
                    document.getElementById('simpleForm').style.display = 'block';
                    document.getElementById('adultForm').classList.remove('active');
                    document.getElementById('studentForm').classList.remove('active');
                    document.getElementById('relationText').textContent = relation;
                    document.getElementById('relationText').style.color = '#667eea';
                }
            }
        }

        // 检查保存按钮状态
        function checkSaveButtonState() {
            const name = document.getElementById('memberName').value.trim();
            const phone = document.getElementById('memberPhone').value.trim();
            const saveBtn = document.getElementById('saveBtn');
            
            if (selectedRelation && name && phone) {
                saveBtn.disabled = false;
            } else {
                saveBtn.disabled = true;
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            getRelationFromUrl();

            const nameInput = document.getElementById('memberName');
            const phoneInput = document.getElementById('memberPhone');

            // 限制手机号只能输入数字
            if (phoneInput) {
                phoneInput.addEventListener('input', function(e) {
                    this.value = this.value.replace(/[^\d]/g, '');
                    checkSaveButtonState();
                });
            }

            if (nameInput) {
                nameInput.addEventListener('input', checkSaveButtonState);
            }

            // 初始状态检查
            checkSaveButtonState();

            // 如果没有从URL获取到关系，显示身份选择弹出层
            if (!selectedRelation) {
                setTimeout(() => {
                    showIdentityPopup();
                }, 500);
            }
        });
    </script>
</body>
</html>
