<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>更换手机号</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: white;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px 10px;
            font-size: 17px;
            font-weight: 600;
        }

        .header {
            display: flex;
            align-items: center;
            padding: 10px 20px 20px;
        }

        .back-arrow {
            font-size: 18px;
            color: #999;
            margin-right: 10px;
            cursor: pointer;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .content {
            padding: 0 20px;
            height: calc(100% - 160px);
            overflow-y: auto;
        }

        .form-container {
            margin-top: 40px;
        }

        .input-group {
            margin-bottom: 30px;
        }

        .input-label {
            font-size: 16px;
            color: #333;
            margin-bottom: 12px;
            display: block;
        }

        .input-field {
            width: 100%;
            height: 50px;
            border: none;
            border-bottom: 1px solid #e0e0e0;
            background: transparent;
            font-size: 16px;
            color: #333;
            outline: none;
            padding: 0;
        }

        .input-field::placeholder {
            color: #999;
            font-size: 16px;
        }

        .input-field:focus {
            border-bottom-color: #667eea;
        }

        .next-btn {
            position: fixed;
            bottom: 120px;
            left: 50%;
            transform: translateX(-50%);
            width: 335px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .next-btn:hover {
            transform: translateX(-50%) translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .next-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: translateX(-50%);
            box-shadow: none;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="status-bar">
            <span class="time">9:41</span>
            <span class="signal">📶 📶 📶 🔋</span>
        </div>

        <div class="header">
            <span class="back-arrow" onclick="goBack()">←</span>
            <span class="page-title">更换手机号</span>
        </div>

        <div class="content">
            <div class="form-container">
                <div class="input-group">
                    <label class="input-label">原手机号码</label>
                    <input type="tel" class="input-field" id="oldPhone" placeholder="请输入原手机号码" maxlength="11">
                </div>

                <div class="input-group">
                    <label class="input-label">新手机号码</label>
                    <input type="tel" class="input-field" id="newPhone" placeholder="请输入新手机号码" maxlength="11">
                </div>
            </div>
        </div>

        <button class="next-btn" id="nextBtn" onclick="handleNext()">下一步</button>
    </div>

    <script>
        // 返回上一页
        function goBack() {
            window.history.back();
        }

        // 处理下一步按钮点击
        function handleNext() {
            const oldPhone = document.getElementById('oldPhone').value.trim();
            const newPhone = document.getElementById('newPhone').value.trim();

            if (!oldPhone) {
                alert('请输入原手机号码');
                return;
            }

            if (!newPhone) {
                alert('请输入新手机号码');
                return;
            }

            // 验证手机号格式
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(oldPhone)) {
                alert('原手机号码格式不正确');
                return;
            }

            if (!phoneRegex.test(newPhone)) {
                alert('新手机号码格式不正确');
                return;
            }

            if (oldPhone === newPhone) {
                alert('新手机号码不能与原手机号码相同');
                return;
            }

            // 跳转到验证码页面，并传递新手机号码
            window.location.href = `verify-phone.html?phone=${newPhone}`;
        }

        // 输入框事件监听
        document.addEventListener('DOMContentLoaded', function() {
            const oldPhoneInput = document.getElementById('oldPhone');
            const newPhoneInput = document.getElementById('newPhone');
            const nextBtn = document.getElementById('nextBtn');

            // 限制只能输入数字
            function limitToNumbers(input) {
                input.addEventListener('input', function(e) {
                    this.value = this.value.replace(/[^\d]/g, '');
                });
            }

            limitToNumbers(oldPhoneInput);
            limitToNumbers(newPhoneInput);

            // 检查按钮状态
            function checkButtonState() {
                const oldPhone = oldPhoneInput.value.trim();
                const newPhone = newPhoneInput.value.trim();
                
                if (oldPhone && newPhone) {
                    nextBtn.disabled = false;
                } else {
                    nextBtn.disabled = true;
                }
            }

            oldPhoneInput.addEventListener('input', checkButtonState);
            newPhoneInput.addEventListener('input', checkButtonState);

            // 初始状态
            checkButtonState();
        });
    </script>
</body>
</html>
