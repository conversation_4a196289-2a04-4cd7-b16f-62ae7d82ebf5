

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: white;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px 10px;
            font-size: 17px;
            font-weight: 600;
        }

        .header {
            display: flex;
            align-items: center;
            padding: 10px 20px 20px;
        }

        .back-arrow {
            font-size: 18px;
            color: #999;
            margin-right: 10px;
            cursor: pointer;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .content {
            padding: 0 20px;
            height: calc(100% - 160px);
            overflow-y: auto;
        }

        /* 编辑模式样式 */
        .edit-mode {
            display: none;
        }

        .edit-mode.active {
            display: block;
        }

        .edit-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background-image: url('https://picsum.photos/160/160?random=1');
            background-size: cover;
            background-position: center;
            margin: 30px auto 40px;
        }

        .edit-form {
            margin-bottom: 40px;
        }

        .form-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 18px 0;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
        }

        .form-item:last-child {
            border-bottom: none;
        }

        .form-label {
            font-size: 16px;
            color: #333;
        }

        .form-value {
            display: flex;
            align-items: center;
            color: #999;
            font-size: 16px;
        }

        .form-value.relation {
            color: #667eea;
        }

        .form-value.gender {
            color: #ff69b4;
        }

        .form-arrow {
            margin-left: 8px;
            color: #999;
            font-size: 14px;
        }

        .save-btn {
            position: fixed;
            bottom: 120px;
            left: 50%;
            transform: translateX(-50%);
            width: 335px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
        }

        /* 隐藏普通模式 */
        .normal-mode.hidden {
            display: none;
        }

        /* 用户信息区域 */
        .user-section {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-image: url('https://picsum.photos/120/120?random=1');
            background-size: cover;
            background-position: center;
            margin-right: 15px;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
        }

        .arrow-right {
            margin-left: 8px;
            color: #999;
            font-size: 14px;
        }

        .edit-btn {
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 15px;
            padding: 6px 16px;
            font-size: 14px;
            margin-top: 8px;
            cursor: pointer;
        }

        /* 家庭成员区域 */
        .family-section {
            margin: 30px 0;
        }

        .family-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }

        .family-members {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .member-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
        }

        .member-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin-bottom: 8px;
            background-size: cover;
            background-position: center;
        }

        .member-avatar.child1 { 
            background-image: url('https://picsum.photos/100/100?random=2');
        }
        .member-avatar.child2 { 
            background-image: url('https://picsum.photos/100/100?random=3');
        }
        .member-avatar.child3 { 
            background-image: url('https://picsum.photos/100/100?random=4');
        }
        .member-avatar.dad { 
            background-image: url('https://picsum.photos/100/100?random=5');
        }
        .member-avatar.add-member {
            background: white;
            border: 2px dashed #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: #999;
        }

        .member-name {
            font-size: 12px;
            color: #666;
        }

        /* 菜单列表 */
        .menu-list {
            margin-top: 30px;
        }

        .menu-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 18px 0;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        .menu-text {
            font-size: 16px;
            color: #333;
        }

        .menu-right {
            display: flex;
            align-items: center;
            color: #999;
            font-size: 14px;
        }

        .version-text {
            margin-right: 8px;
        }

        /* 底部导航样式 */
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            margin-bottom: 4px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 导航图标样式 */
        .nav-item:nth-child(1) .nav-icon::before {
            content: '';
            width: 20px;
            height: 18px;
            border: 2px solid #999;
            border-bottom: none;
            border-radius: 4px 4px 0 0;
            position: relative;
        }

        .nav-item:nth-child(1) .nav-icon::after {
            content: '';
            position: absolute;
            bottom: -2px;
            width: 24px;
            height: 8px;
            border: 2px solid #999;
            border-top: none;
            border-radius: 0 0 2px 2px;
        }

        .nav-item:nth-child(2) .nav-icon::before {
            content: '';
            width: 16px;
            height: 20px;
            border: 2px solid #999;
            border-radius: 2px;
            position: relative;
        }

        .nav-item:nth-child(2) .nav-icon::after {
            content: '';
            position: absolute;
            top: 6px;
            left: 50%;
            transform: translateX(-50%);
            width: 8px;
            height: 1px;
            background: #999;
            box-shadow: 0 3px 0 #999, 0 6px 0 #999;
        }

        .nav-item:nth-child(3) .nav-icon::before {
            content: '';
            width: 20px;
            height: 16px;
            border: 2px solid #999;
            border-radius: 2px;
            position: relative;
        }

        .nav-item:nth-child(3) .nav-icon::after {
            content: '';
            position: absolute;
            bottom: 2px;
            left: 3px;
            width: 3px;
            height: 6px;
            background: #999;
            box-shadow: 4px 2px 0 #999, 8px -1px 0 #999, 12px 1px 0 #999;
        }

        .nav-item:nth-child(4) .nav-icon::before {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid #999;
            border-radius: 50%;
            position: relative;
        }

        .nav-item:nth-child(4) .nav-icon::after {
            content: '';
            position: absolute;
            top: 4px;
            left: 50%;
            transform: translateX(-50%);
            width: 6px;
            height: 6px;
            background: #999;
            border-radius: 50%;
        }

        .nav-text {
            font-size: 12px;
            color: #666;
            transition: color 0.3s ease;
        }

        .nav-item.active .nav-icon::before,
        .nav-item.active .nav-icon::after {
            border-color: #667eea;
            background-color: #667eea;
        }

        .nav-item.active .nav-text {
            color: #667eea;
        }

        .nav-item.active:nth-child(4) .nav-icon::after {
            background: #667eea;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="status-bar">
            <span class="time">9:41</span>
            <span class="signal">📶 📶 📶 🔋</span>
        </div>

        <div class="header">
            <span class="back-arrow">←</span>
            <span class="page-title" id="pageTitle">个人中心</span>
        </div>

        <div class="content">
            <!-- 普通模式 -->
            <div class="normal-mode">
                <!-- 用户信息区域 -->
                <div class="user-section">
                    <div class="user-avatar"></div>
                    <div class="user-info">
                        <div class="user-name">
                            Rachel
                            <span class="arrow-right">›</span>
                        </div>
                        <button class="edit-btn">编辑</button>
                    </div>
                </div>

                <!-- 家庭成员区域 -->
                <div class="family-section">
                    <div class="family-title">家庭成员 (4)</div>
                    <div class="family-members">
                        <div class="member-item">
                            <div class="member-avatar child1"></div>
                            <div class="member-name">孩子1</div>
                        </div>
                        <div class="member-item">
                            <div class="member-avatar child2"></div>
                            <div class="member-name">孩子2</div>
                        </div>
                        <div class="member-item">
                            <div class="member-avatar child3"></div>
                            <div class="member-name">孩子3</div>
                        </div>
                        <div class="member-item">
                            <div class="member-avatar dad"></div>
                            <div class="member-name">爸爸</div>
                        </div>
                        <div class="member-item">
                            <div class="member-avatar add-member">+</div>
                            <div class="member-name">成员</div>
                        </div>
                    </div>
                </div>

                <!-- 菜单列表 -->
                <div class="menu-list">
                    <div class="menu-item" onclick="handleMenuClick('message')">
                        <span class="menu-text">消息中心</span>
                        <span class="menu-right">›</span>
                    </div>
                    <div class="menu-item" onclick="handleMenuClick('feedback')">
                        <span class="menu-text">意见反馈</span>
                        <span class="menu-right">›</span>
                    </div>
                    <div class="menu-item" onclick="handleMenuClick('version')">
                        <span class="menu-text">版本号</span>
                        <div class="menu-right">
                            <span class="version-text">v0.1</span>
                        </div>
                    </div>
                    <div class="menu-item" onclick="handleMenuClick('switch')">
                        <span class="menu-text">切换身份</span>
                        <span class="menu-right">›</span>
                    </div>
                    <div class="menu-item" onclick="handleMenuClick('logout')">
                        <span class="menu-text">退出登录</span>
                        <span class="menu-right">›</span>
                    </div>
                </div>
            </div>

            <!-- 编辑模式 -->
            <div class="edit-mode">
                <div class="edit-avatar"></div>
                <div class="edit-form">
                    <div class="form-item">
                        <span class="form-label">成员关系</span>
                        <div class="form-value relation">
                            约翰
                            <span class="form-arrow">›</span>
                        </div>
                    </div>
                    <div class="form-item">
                        <span class="form-label">名字</span>
                        <div class="form-value">
                            Rachel
                            <span class="form-arrow">›</span>
                        </div>
                    </div>
                    <div class="form-item">
                        <span class="form-label">性别</span>
                        <div class="form-value gender">
                            ♀ 女
                            <span class="form-arrow">›</span>
                        </div>
                    </div>
                    <div class="form-item">
                        <span class="form-label">出生年月</span>
                        <div class="form-value">
                            1900.01.01
                            <span class="form-arrow">›</span>
                        </div>
                    </div>
                    <div class="form-item" onclick="goToChangePhone()">
                        <span class="form-label">手机号码</span>
                        <div class="form-value">
                            1386****000
                            <span class="form-arrow">›</span>
                        </div>
                    </div>
                </div>
                <button class="save-btn">保存</button>
            </div>
        </div>

        <div class="bottom-nav">
            <div class="nav-item" onclick="goToPage('homepage.html')">
                <div class="nav-icon"></div>
                <div class="nav-text">首页</div>
            </div>
            <div class="nav-item" onclick="goToPage('past-essays.html')">
                <div class="nav-icon"></div>
                <div class="nav-text">过往作文</div>
            </div>
            <div class="nav-item" onclick="goToPage('growth-data.html')">
                <div class="nav-icon"></div>
                <div class="nav-text">成长数据</div>
            </div>
            <div class="nav-item active">
                <div class="nav-icon"></div>
                <div class="nav-text">个人中心</div>
            </div>
        </div>
    </div>

    <script>
        function goToPage(url) {
            window.location.href = url;
        }

        function goToChangePhone() {
            window.location.href = 'change-phone.html';
        }

        function handleMenuClick(type) {
            switch(type) {
                case 'message':
                    alert('消息中心功能开发中');
                    break;
                case 'feedback':
                    alert('意见反馈功能开发中');
                    break;
                case 'version':
                    alert('当前版本：v0.1');
                    break;
                case 'switch':
                    alert('切换身份功能开发中');
                    break;
                case 'logout':
                    if(confirm('确定要退出登录吗？')) {
                        window.location.href = 'login.html';
                    }
                    break;
            }
        }

        // 编辑按钮点击事件
        document.querySelector('.edit-btn').addEventListener('click', function() {
            enterEditMode();
        });

        // 进入编辑模式
        function enterEditMode() {
            document.querySelector('.normal-mode').classList.add('hidden');
            document.querySelector('.edit-mode').classList.add('active');
            document.getElementById('pageTitle').textContent = '编辑个人信息';
        }

        // 退出编辑模式
        function exitEditMode() {
            document.querySelector('.normal-mode').classList.remove('hidden');
            document.querySelector('.edit-mode').classList.remove('active');
            document.getElementById('pageTitle').textContent = '个人中心';
        }

        // 保存按钮点击事件
        document.addEventListener('DOMContentLoaded', function() {
            const saveBtn = document.querySelector('.save-btn');
            if (saveBtn) {
                saveBtn.addEventListener('click', function() {
                    alert('保存成功！');
                    exitEditMode();
                });
            }
        });

        // 用户信息点击事件
        document.querySelector('.user-name').addEventListener('click', function() {
            alert('查看个人详情功能开发中');
        });

        // 家庭成员点击事件
        document.querySelectorAll('.member-item').forEach(item => {
            item.addEventListener('click', function() {
                const memberName = this.querySelector('.member-name').textContent;
                if (memberName === '成员') {
                    window.location.href = 'select-member.html';
                } else {
                    alert(`查看${memberName}详情功能开发中`);
                }
            });
        });

        // 返回按钮点击事件
        document.querySelector('.back-arrow').addEventListener('click', function() {
            // 如果在编辑模式，返回到普通模式
            if (document.querySelector('.edit-mode').classList.contains('active')) {
                exitEditMode();
            } else {
                window.history.back();
            }
        });
    </script>
</body>
</html>
