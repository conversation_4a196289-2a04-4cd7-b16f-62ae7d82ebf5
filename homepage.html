<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Awriter首页</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: white;
            border-radius: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px 10px;
            font-size: 17px;
            font-weight: 600;
        }

        .time { color: #000; }
        .signal { color: #000; }

        .content {
            padding: 0 20px;
            height: calc(100% - 140px);
            overflow-y: auto;
        }

        /* 用户信息区域 */
        .user-section {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-image: url('https://picsum.photos/100/100?random=1');
            background-size: cover;
            background-position: center;
            margin-right: 15px;
        }

        .user-name {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }

        /* 孩子选择区域 */
        .children-section {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
        }

        .child-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
        }

        .child-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 8px;
            position: relative;
            background-size: cover;
            background-position: center;
        }

        .child-avatar.child1 { 
            background-image: url('https://picsum.photos/120/120?random=2');
        }
        .child-avatar.child2 { 
            background-image: url('https://picsum.photos/120/120?random=3');
        }
        .child-avatar.child3 { 
            background-image: url('https://picsum.photos/120/120?random=4');
        }
        .child-avatar.add-btn {
            background: #f0f0f0;
            border: 2px dashed #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #999;
        }

        .child-name {
            font-size: 14px;
            color: #666;
        }

        /* 当前孩子信息 */
        .current-child {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .current-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-image: url('https://picsum.photos/80/80?random=2');
            background-size: cover;
            background-position: center;
            margin-right: 12px;
        }

        .current-info h3 {
            font-size: 16px;
            color: #333;
            margin-bottom: 2px;
        }

        .current-info .birth-info {
            font-size: 12px;
            color: #999;
        }

        .location-icon {
            color: #ff69b4;
            margin-left: 5px;
        }

        /* 学校信息 */
        .school-info {
            font-size: 16px;
            color: #333;
            margin-bottom: 20px;
        }

        /* 学期信息 */
        .semester-info {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }

        /* 作业列表 */
        .assignment-list {
            margin-bottom: 20px;
        }

        .assignment-item {
            background: white;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
        }

        .assignment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .assignment-title {
            font-size: 14px;
            color: #666;
        }

        .assignment-score {
            font-size: 20px;
            font-weight: bold;
            color: #ff6b6b;
        }

        .assignment-content {
            font-size: 16px;
            color: #333;
            margin-bottom: 8px;
        }

        .assignment-time {
            font-size: 12px;
            color: #999;
        }

        .upload-btn {
            position: absolute;
            right: 15px;
            bottom: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 6px 12px;
            font-size: 12px;
            cursor: pointer;
        }

        .arrow-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #ccc;
            font-size: 16px;
        }

        /* 底部导航 */
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            margin-bottom: 4px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 首页图标 */
        .nav-item:nth-child(1) .nav-icon::before {
            content: '';
            width: 20px;
            height: 18px;
            border: 2px solid #999;
            border-bottom: none;
            border-radius: 4px 4px 0 0;
            position: relative;
        }

        .nav-item:nth-child(1) .nav-icon::after {
            content: '';
            position: absolute;
            bottom: -2px;
            width: 24px;
            height: 8px;
            border: 2px solid #999;
            border-top: none;
            border-radius: 0 0 2px 2px;
        }

        /* 过往作文图标 */
        .nav-item:nth-child(2) .nav-icon::before {
            content: '';
            width: 16px;
            height: 20px;
            border: 2px solid #999;
            border-radius: 2px;
            position: relative;
        }

        .nav-item:nth-child(2) .nav-icon::after {
            content: '';
            position: absolute;
            top: 6px;
            left: 50%;
            transform: translateX(-50%);
            width: 8px;
            height: 1px;
            background: #999;
            box-shadow: 0 3px 0 #999, 0 6px 0 #999;
        }

        /* 成长数据图标 */
        .nav-item:nth-child(3) .nav-icon::before {
            content: '';
            width: 20px;
            height: 16px;
            border: 2px solid #999;
            border-radius: 2px;
            position: relative;
        }

        .nav-item:nth-child(3) .nav-icon::after {
            content: '';
            position: absolute;
            bottom: 2px;
            left: 3px;
            width: 3px;
            height: 6px;
            background: #999;
            box-shadow: 4px 2px 0 #999, 8px -1px 0 #999, 12px 1px 0 #999;
        }

        /* 个人中心图标 */
        .nav-item:nth-child(4) .nav-icon::before {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid #999;
            border-radius: 50%;
            position: relative;
        }

        .nav-item:nth-child(4) .nav-icon::after {
            content: '';
            position: absolute;
            top: 4px;
            left: 50%;
            transform: translateX(-50%);
            width: 6px;
            height: 6px;
            background: #999;
            border-radius: 50%;
        }

        .nav-text {
            font-size: 12px;
            color: #666;
            transition: color 0.3s ease;
        }

        /* 激活状态 */
        .nav-item.active .nav-icon::before,
        .nav-item.active .nav-icon::after {
            border-color: #667eea;
            background-color: #667eea;
        }

        .nav-item.active .nav-text {
            color: #667eea;
        }

        /* 首页激活状态特殊处理 */
        .nav-item.active:nth-child(1) .nav-icon::after {
            background-color: transparent;
            border-color: #667eea;
        }

        /* 成长数据激活状态特殊处理 */
        .nav-item.active:nth-child(3) .nav-icon::after {
            background: #667eea;
            box-shadow: 4px 2px 0 #667eea, 8px -1px 0 #667eea, 12px 1px 0 #667eea;
        }

        /* 个人中心激活状态特殊处理 */
        .nav-item.active:nth-child(4) .nav-icon::after {
            background: #667eea;
        }

        .divider {
            text-align: center;
            color: #ccc;
            font-size: 12px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="status-bar">
            <span class="time">9:41</span>
            <span class="signal">📶 📶 📶 🔋</span>
        </div>

        <div class="content">
            <!-- 用户信息区域 -->
            <div class="user-section">
                <div class="user-avatar"></div>
                <div class="user-name">Rachel</div>
            </div>

            <!-- 孩子选择区域 -->
            <div class="children-section">
                <div class="child-item">
                    <div class="child-avatar child1"></div>
                    <div class="child-name">孩子1</div>
                </div>
                <div class="child-item">
                    <div class="child-avatar child2"></div>
                    <div class="child-name">孩子2</div>
                </div>
                <div class="child-item">
                    <div class="child-avatar child3"></div>
                    <div class="child-name">孩子3</div>
                </div>
                <div class="child-item">
                    <div class="child-avatar add-btn">+</div>
                    <div class="child-name">孩子</div>
                </div>
            </div>

            <!-- 当前孩子信息 -->
            <div class="current-child">
                <div class="current-avatar"></div>
                <div class="current-info">
                    <h3>孩子1 <span class="location-icon">📍</span></h3>
                    <div class="birth-info">2020.06.03 5岁</div>
                </div>
            </div>

            <!-- 学校信息 -->
            <div class="school-info">江头中心小学 一年一班 09号</div>

            <!-- 学期信息 -->
            <div class="semester-info">2025~2026学年上学期</div>

            <!-- 作业列表 -->
            <div class="assignment-list">
                <div class="assignment-item">
                    <div class="assignment-header">
                        <div class="assignment-title">第三单元 单元作文 全命题</div>
                    </div>
                    <div class="assignment-content">我的植物朋友</div>
                    <div class="assignment-time">2025.03.30 14:59</div>
                    <button class="upload-btn">上传作文</button>
                    <div class="arrow-icon">›</div>
                </div>

                <div class="assignment-item">
                    <div class="assignment-header">
                        <div class="assignment-title">第二单元 单元作文 全命题</div>
                        <div class="assignment-score">28<span style="font-size: 12px;">分</span></div>
                    </div>
                    <div class="assignment-content">我的植物朋友</div>
                    <div class="assignment-time">2025.05.08 11:22</div>
                    <div class="arrow-icon">›</div>
                </div>

                <div class="assignment-item">
                    <div class="assignment-header">
                        <div class="assignment-title">第一单元 单元作文 全命题</div>
                        <div class="assignment-score">28<span style="font-size: 12px;">分</span></div>
                    </div>
                    <div class="assignment-content">我的植物朋友</div>
                    <div class="assignment-time">2025.05.08 11:22</div>
                    <div class="arrow-icon">›</div>
                </div>
            </div>

            <div class="divider">——— 我是萌萌的分割线 ———</div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item active" onclick="goToPage('homepage.html')">
                <div class="nav-icon"></div>
                <div class="nav-text">首页</div>
            </div>
            <div class="nav-item" onclick="goToPage('past-essays.html')">
                <div class="nav-icon"></div>
                <div class="nav-text">过往作文</div>
            </div>
            <div class="nav-item" onclick="goToPage('growth-data.html')">
                <div class="nav-icon"></div>
                <div class="nav-text">成长数据</div>
            </div>
            <div class="nav-item" onclick="goToPage('profile.html')">
                <div class="nav-icon"></div>
                <div class="nav-text">个人中心</div>
            </div>
        </div>
    </div>

    <script>
        // 底部导航切换和跳转
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                // 移除所有激活状态
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                // 添加当前激活状态
                this.classList.add('active');
            });
        });

        // 页面跳转函数
        function goToPage(url) {
            window.location.href = url;
        }

        // 孩子选择
        document.querySelectorAll('.child-item').forEach((item, index) => {
            item.addEventListener('click', function() {
                const childName = this.querySelector('.child-name').textContent;
                if (childName !== '孩子') {
                    // 更新当前孩子信息
                    document.querySelector('.current-info h3').innerHTML = `${childName} <span class="location-icon">📍</span>`;
                    
                    // 更新当前孩子头像
                    const currentAvatar = document.querySelector('.current-avatar');
                    const childAvatar = this.querySelector('.child-avatar');
                    const backgroundImage = window.getComputedStyle(childAvatar).backgroundImage;
                    currentAvatar.style.backgroundImage = backgroundImage;
                }
            });
        });

        // 作业项点击
        document.querySelectorAll('.assignment-item').forEach(item => {
            item.addEventListener('click', function(e) {
                if (!e.target.classList.contains('upload-btn')) {
                    // 跳转到作文详情页面
                    window.location.href = 'essay-detail.html';
                }
            });
        });

        // 上传作文按钮
        document.querySelector('.upload-btn').addEventListener('click', function(e) {
            e.stopPropagation();
            window.location.href = 'essay-requirements.html';
        });
    </script>
</body>
</html>

